import { Container } from 'typedi';
import { Authorized, Body, CurrentUser, Delete, Get, HttpError, JsonController, Param, Post, Put, QueryParam } from 'routing-controllers';
import { ICurrentUser } from '@app/interfaces';
import { UserType } from '@app/utils/enums/usertype';
import { TagDto } from './TagDto';
import { TagService } from './TagService';

@JsonController('/seasonal-management/tag')
export class TagController {
  private tagService: TagService;

  constructor() {
    this.tagService = Container.get(TagService);
  }

  @Get()
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async listTags(
    @CurrentUser() user: ICurrentUser,
    @QueryParam('page') page: number = 1,
    @QueryParam('size') size: number = 100,
    @QueryParam('filters') filters?: string
  ) {
    try {
      return await this.tagService.listTags(user, page, size, filters && JSON.parse(filters));
    } catch (error: any) {
      throw error;
    }
  }

  @Get('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTag(
    @CurrentUser() user: ICurrentUser,
    @Param('id') tagId: string
  ) {
    try {
      return await this.tagService.getTagById(tagId, user);
    } catch (error: any) {
      if (error instanceof HttpError) throw error;
      throw new HttpError(500, 'Internal Server Error');
    }
  }

  @Post()
  @Authorized([UserType.SYSTEM_USER])
  async createTag(
    @CurrentUser() user: ICurrentUser,
    @Body() body: TagDto
  ) {
    try {
      return await this.tagService.createTag(user, body);
    } catch (error: any) {
      throw error;
    }
  }

  @Put('/:id')
  @Authorized([UserType.SYSTEM_USER])
  async updateTag(
    @CurrentUser() user: ICurrentUser,
    @Param('id') tagId: string,
    @Body() body: Partial<TagDto>
  ) {
    try {
      return await this.tagService.updateTag(tagId, body, user);
    } catch (error: any) {
      throw error;
    }
  }

  @Delete('/:id')
  @Authorized([UserType.SYSTEM_USER])
  async deleteTag(
    @Param('id') tagId: string,
    @CurrentUser() user: ICurrentUser
  ) {
    try {
      return await this.tagService.deleteTag(tagId, user);
    } catch (error: any) {
      throw error;
    }
  }
}
