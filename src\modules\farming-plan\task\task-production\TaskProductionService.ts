import { FrappeService } from '@app/services/frappe/FrappeService';
import Container, { Service } from 'typedi';
import { ICurrentUser } from '@app/interfaces';
import { ERPExecute, ERPExecuteWithTransaction } from '@app/loaders/pglib/PGDB';
import { randomUUID, verify } from 'crypto';
import { OtherService } from '@app/services/frappe/OtherService';
import { IIotProductionQuantity } from '@app/interfaces/IIotProductionQuantity';
import { SqlQuery } from '@app/interfaces/ISQLQuery';
import e from 'express';
import moment from 'moment';
import { getTotalProductionQtyInCrop, updateTotalProductionQtyInCrop } from './TaskProductionValidator';
import { StockEntryService } from '@app/modules/stock-v3/stock-entry/StockEntryService';
import { IMaterialTotal } from '@app/modules/stock-v3/stock-entry/StockEntryValidator';
import { CropService } from '@app/modules/crop-manage/crop/CropService';
import { IGeneralDoc } from '@app/interfaces/IGeneralDoc';
import { parseFilterParams } from '@app/utils/helpers/helper';

@Service()
export class TaskProductionService {
  constructor(
    private frappeService: FrappeService,
    private otherService: OtherService,
    private stockEntryService: StockEntryService,
    private cropService: CropService,
  ) {
    this.frappeService = Container.get(FrappeService);
    this.otherService = Container.get(OtherService);
    this.stockEntryService = Container.get(StockEntryService);
    this.cropService = Container.get(CropService);
  }
  async getAllTaskProduction(user: ICurrentUser, params: IGeneralDoc) {
    try {
      let filters: any[] = [];
      if (params.filters) {
        let paramsFilters = JSON.parse(params.filters);
        paramsFilters.forEach((element: any) => {
          filters.push(element);
        });
      }

      let page = params.page ? parseInt(params.page) : 1;
      let size = params.size ? parseInt(params.size) : 10000;
      let sqlConditionStr = parseFilterParams(filters);

      const query: string = `
        SELECT
          "tabiot_production_quantity".name,
          "tabiot_production_quantity".description,
          "tabiot_production_quantity".task_id,
          "tabiot_production_quantity".product_id,
          "tabiot_production_quantity".exp_quantity,
          "tabiot_production_quantity".quantity,
          "tabiot_production_quantity".lost_quantity,
          "tabiot_production_quantity".issued_quantity,
          "tabiot_production_quantity".finished_quantity,
          "tabiot_production_quantity".total_qty_in_crop,
          "tabiot_production_quantity".creation,
          "tabiot_production_quantity".modified,
          "tabiot_production_quantity".draft_quantity,
          "tabiot_production_quantity".active_uom,
          "tabiot_production_quantity".active_conversion_factor,
          "tabItem".label AS item_label,
          "tabUOM".name AS uom_id,
          "tabUOM".uom_name AS uom_label,
          "activeUOM".name AS active_uom_id,
          "activeUOM".uom_name AS active_uom_label,
          1 AS conversion_factor,
          1 AS ratio,
          (
            SELECT json_agg(
              json_build_object(
                'uom_id', "tabUOM Conversion Detail".uom,
                'uom_label', uom_table.uom_name,
                'conversion_factor', "tabUOM Conversion Detail".conversion_factor
              )
              ORDER BY "tabUOM Conversion Detail".conversion_factor ASC
            )
            FROM "tabUOM Conversion Detail"
            LEFT JOIN "tabUOM" AS uom_table ON "tabUOM Conversion Detail".uom = uom_table.name
            WHERE "tabUOM Conversion Detail".parent = "tabiot_production_quantity".product_id
          ) AS uoms
        FROM
          "tabiot_production_quantity"
        LEFT JOIN
          "tabItem" ON "tabItem".name = "tabiot_production_quantity".product_id
        LEFT JOIN
          "tabUOM" ON "tabUOM".name = "tabItem".stock_uom
        LEFT JOIN
          "tabUOM" AS "activeUOM" ON "activeUOM".name = "tabiot_production_quantity".active_uom
        WHERE TRUE
          ${sqlConditionStr}
        ORDER BY "tabItem".label ASC
        LIMIT ${size} OFFSET ${(page - 1) * size}
      `;

      const taskItemList: any = await ERPExecute(query, []);
      const queryCount: string = `
        SELECT
          COUNT("tabiot_production_quantity".name) AS total
        FROM
          "tabiot_production_quantity"
        LEFT JOIN
          "tabItem" ON "tabItem".name = "tabiot_production_quantity".product_id
        LEFT JOIN
          "tabUOM" ON "tabUOM".name = "tabItem".stock_uom
        WHERE TRUE
          ${sqlConditionStr}
      `;
      const total: any = await ERPExecute(queryCount, []);
      return {
        data: taskItemList,
        pagination: {
          pageNumber: page,
          pageSize: size,
          totalElements: total[0].total,
          totalPages: Math.ceil(total[0].total / size),
        },
      };

    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }

  async getTotalProductionQtyInCrop(params: { user: ICurrentUser; item_id: string; crop_id: string }) {
    try {
      const paramsMaterialTotal: any = {
        item_code: params.item_id,
        iot_crop: params.crop_id,
      } as IMaterialTotal;
      const totalQtyInCropList = await this.stockEntryService.getMaterialTotalBalanceForProduction(
        params.user,
        paramsMaterialTotal,
      );
      return totalQtyInCropList;
    } catch (error) {
      throw error;
    }
  }

  async createTaskProductionAdmin(user: ICurrentUser, body: IIotProductionQuantity[]) {
    try {
      const sqlArray: SqlQuery[] = [];
      for (const item of body) {
        const name = randomUUID();
        const creation = moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss');

        // Get crop id from task id
        const crop = await this.cropService.getCropByTask(user, { task_id: item.task_id });
        const totalQtyInCrop = await this.getTotalProductionQtyInCrop({
          user,
          item_id: item.product_id,
          crop_id: crop.name,
        });
        const findTotalQtyInCrop = totalQtyInCrop.find(i => i.item_id === item.product_id);

        // Build dynamic query based on provided fields (similar to update method)
        const fields: string[] = ['name', 'creation', 'modified'];
        const placeholders: string[] = ['$1', '$2', '$3'];
        const params: any[] = [name, creation, creation];
        let paramIndex = 4;

        // Always include required fields
        if (item.task_id !== undefined) {
          fields.push('task_id');
          placeholders.push(`$${paramIndex}`);
          params.push(item.task_id);
          paramIndex++;
        }
        if (item.product_id !== undefined) {
          fields.push('product_id');
          placeholders.push(`$${paramIndex}`);
          params.push(item.product_id);
          paramIndex++;
        }

        // Optional fields - only include if provided
        if (item.quantity !== undefined) {
          fields.push('quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.quantity);
          paramIndex++;
        }
        if (item.description !== undefined) {
          fields.push('description');
          placeholders.push(`$${paramIndex}`);
          params.push(item.description);
          paramIndex++;
        }
        if (item.exp_quantity !== undefined) {
          fields.push('exp_quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.exp_quantity);
          paramIndex++;
        }
        if (item.lost_quantity !== undefined) {
          fields.push('lost_quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.lost_quantity);
          paramIndex++;
        }
        if (item.finished_quantity !== undefined) {
          fields.push('finished_quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.finished_quantity);
          paramIndex++;
        }
        if (item.issued_quantity !== undefined) {
          fields.push('issued_quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.issued_quantity);
          paramIndex++;
        }
        if (item.draft_quantity !== undefined) {
          fields.push('draft_quantity');
          placeholders.push(`$${paramIndex}`);
          params.push(item.draft_quantity);
          paramIndex++;
        }

        // UOM related fields - handle active_uom and active_conversion_factor
        if (item.active_uom !== undefined) {
          fields.push('active_uom');
          placeholders.push(`$${paramIndex}`);
          params.push(item.active_uom);
          paramIndex++;
        }
        if (item.active_conversion_factor !== undefined) {
          fields.push('active_conversion_factor');
          placeholders.push(`$${paramIndex}`);
          params.push(item.active_conversion_factor);
          paramIndex++;
        }

        // Always include total_qty_in_crop
        fields.push('total_qty_in_crop');
        placeholders.push(`$${paramIndex}`);
        params.push(findTotalQtyInCrop ? findTotalQtyInCrop.total_qty : 0);

        const sqlQuery: SqlQuery = {
          query: `
            INSERT INTO tabiot_production_quantity
            (${fields.join(', ')})
            VALUES (${placeholders.join(', ')})
            RETURNING *;
          `,
          params: params,
        };
        sqlArray.push(sqlQuery);
      }

      const response = await ERPExecuteWithTransaction(sqlArray);

      // Update total_qty_in_crop for all tabiot_production_quantity with the given product_id
      for (const item of body) {
        await this.updateTotalProductionQtyInCrop(user, { task_id: item.task_id, item_id: item.product_id });
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  async updateTaskProductionAdmin(user: ICurrentUser, body: IIotProductionQuantity[]) {
    try {
      const sqlArrayUpdateQuantity: SqlQuery[] = [];
      const sqlArrayUpdateTotalQty: SqlQuery[] = [];

      for (const item of body) {
        // Build dynamic query based on non-null fields
        const fields: string[] = [];
        const params: any[] = [item.name];
        let paramIndex = 2; // Start from 2 because 1 is reserved for the item name

        if (item.quantity !== undefined) {
          fields.push(`quantity = $${paramIndex}`);
          params.push(item.quantity);
          paramIndex++;
        }
        if (item.description !== undefined) {
          fields.push(`description = $${paramIndex}`);
          params.push(item.description);
          paramIndex++;
        }
        if (item.task_id !== undefined) {
          fields.push(`task_id = $${paramIndex}`);
          params.push(item.task_id);
          paramIndex++;
        }
        if (item.product_id !== undefined) {
          fields.push(`product_id = $${paramIndex}`);
          params.push(item.product_id);
          paramIndex++;
        }
        if (item.exp_quantity !== undefined) {
          fields.push(`exp_quantity = $${paramIndex}`);
          params.push(item.exp_quantity);
          paramIndex++;
        }
        if (item.lost_quantity !== undefined) {
          fields.push(`lost_quantity = $${paramIndex}`);
          params.push(item.lost_quantity);
          paramIndex++;
        }
        if (item.finished_quantity !== undefined) {
          fields.push(`finished_quantity = $${paramIndex}`);
          params.push(item.finished_quantity);
          paramIndex++;
        }
        if (item.issued_quantity !== undefined) {
          fields.push(`issued_quantity = $${paramIndex}`);
          params.push(item.issued_quantity);
          paramIndex++;
        }
        if (item.draft_quantity !== undefined) {
          fields.push(`draft_quantity = $${paramIndex}`);
          params.push(item.draft_quantity);
          paramIndex++;
        }
        // UOM related fields
        if (item.active_uom !== undefined) {
          fields.push(`active_uom = $${paramIndex}`);
          params.push(item.active_uom);
          paramIndex++;
        }
        if (item.active_conversion_factor !== undefined) {
          fields.push(`active_conversion_factor = $${paramIndex}`);
          params.push(item.active_conversion_factor);
          paramIndex++;
        }

        // Always update modified timestamp
        fields.push(`modified = $${paramIndex}`);
        params.push(moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'));

        const sqlQueryUpdateQuantity: SqlQuery = {
          query: `
            UPDATE tabiot_production_quantity
            SET ${fields.join(', ')}
            WHERE name = $1;
          `,
          params: params,
        };
        sqlArrayUpdateQuantity.push(sqlQueryUpdateQuantity);
      }

      // Execute the first batch of queries
      await ERPExecuteWithTransaction(sqlArrayUpdateQuantity);

      for (const item of body) {
        // Get crop id from task id
        const crop = await this.cropService.getCropByTask(user, { task_id: item.task_id });
        // Then, calculate total_qty_in_crop for the current item and crop
        const totalQtyInCrop = await this.getTotalProductionQtyInCrop({
          user,
          item_id: item.product_id,
          crop_id: crop.name,
        });
        const findTotalQtyInCrop = totalQtyInCrop.find(i => i.item_id === item.product_id);

        // Finally, update total_qty_in_crop
        const sqlQueryUpdateTotalQty: SqlQuery = {
          query: `
            UPDATE tabiot_production_quantity
            SET total_qty_in_crop = $2
            WHERE name = $1;
          `,
          params: [item.name, findTotalQtyInCrop ? findTotalQtyInCrop.total_qty : 0],
        };
        sqlArrayUpdateTotalQty.push(sqlQueryUpdateTotalQty);
      }

      // Execute the second batch of queries
      const response = await ERPExecuteWithTransaction(sqlArrayUpdateTotalQty);

      // Update total_qty_in_crop for all tabiot_production_quantity with the given product_id
      for (const item of body) {
        await this.updateTotalProductionQtyInCrop(user, { task_id: item.task_id, item_id: item.product_id });
      }

      return response;
    } catch (error) {
      throw error;
    }
  }


  async deleteTaskProductionAdmin(user: ICurrentUser, params: { name: string }) {
    const deleteRc: any = await this.frappeService.generalDelete({
      name: params.name,
      doc_name: 'iot_production_quantity',
    });
    return deleteRc;
  }

  async updateTotalProductionQtyInCrop(user: ICurrentUser, params: { task_id: string; item_id: string }) {
    try {
      //get crop id from task id
      const crop = await this.cropService.getCropByTask(user, { task_id: params.task_id });
      // const totalQtyInCrop = await getTotalProductionQtyInCrop({ item_id: params.item_id, task_id: params.task_id });
      const totalQtyInCrop = await this.getTotalProductionQtyInCrop({
        user,
        item_id: params.item_id,
        crop_id: crop.name,
      });

      //get task list in crop
      const taskList = await ERPExecute(
        `
        WITH temp_table AS (
        SELECT
            crop.name AS crop_id,
            plan.name AS plan_id
        FROM tabiot_crop AS crop
        INNER JOIN
          tabiot_farming_plan AS plan
          ON plan.crop = crop.name
        INNER JOIN
          tabiot_farming_plan_state AS state
          ON state.farming_plan = plan.name
        INNER JOIN
          tabiot_farming_plan_task AS task
          ON task.farming_plan_state = state.name
        INNER JOIN
          tabiot_production_quantity AS production_task
          ON production_task.task_id = task.name
        WHERE
          task.name = $1)
        SELECT
          task.name AS task_id
        FROM
          tabiot_farming_plan_task AS task
        INNER JOIN
          tabiot_farming_plan_state AS state
          ON task.farming_plan_state = state.name
        INNER JOIN
          temp_table
          ON state.farming_plan = temp_table.plan_id
        `,
        [params.task_id],
      );
      const taskListSQL = taskList.map((task: any) => `'${task.task_id}'`).join(','); // Then, update total_qty_in_crop for all tabiot_production_quantity with the given product_id
      const sqlQuery: SqlQuery = {
        query: `
              UPDATE tabiot_production_quantity
              SET total_qty_in_crop = $2
              WHERE product_id = $1 AND task_id IN (${taskListSQL});
              `,
        params: [params.item_id, totalQtyInCrop.length ? totalQtyInCrop[0].total_qty : 0],
      };

      // Execute the query
      const response = await ERPExecuteWithTransaction([sqlQuery]);

      return response;
    } catch (error) {
      throw error;
    }
  }
}
