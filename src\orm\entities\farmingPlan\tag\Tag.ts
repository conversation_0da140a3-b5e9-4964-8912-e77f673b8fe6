// File: /home/<USER>/VIIS/iot-backend-typescript/src/orm/entities/farmingPlan/tag/Tag.ts

import { Column, Entity, JoinColumn, OneToMany, PrimaryColumn } from 'typeorm';
import { CustomBaseEntity } from '../../base/Base';
import { Crop } from '../crop/Crop';

@Entity({ name: 'tabiot_tag' })
export class IoTTag extends CustomBaseEntity {
    @PrimaryColumn({ type: 'varchar', length: 140 })
    name: string;

    @Column({ type: 'varchar', length: 140, nullable: true })
    label?: string;

    @Column({ type: 'varchar', length: 140, nullable: true })
    color?: string;

    @Column({ type: 'varchar', length: 140, nullable: true })
    customer_id?: string;

    //column and relation OneToMany to tabiot_crop
    @Column({ type: 'varchar', length: 140, nullable: true })
    crop_id?: string;

    @OneToMany(() => Crop, crop => crop.tag)
    crop?: Crop;
}